# =============================================
# 图书管理系统配置文件
# 环境: 开发环境
# 创建时间: 2024-01-15
# =============================================

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: root
    password: 123456
    
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: LibraryHikariPool
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000
    password: 
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      enabled: true

# MyBatis-Plus配置
mybatis-plus:
  # 配置扫描路径
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.demo.po
  
  # 全局配置
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: enableFlag
      logic-delete-value: F
      logic-not-delete-value: T
      update-strategy: not_null
      insert-strategy: not_null
      select-strategy: not_null
    
    # 是否控制台 print mybatis-plus 的 LOGO
    banner: false

  # 原生配置
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 枚举处理
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 缓存配置
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false

# 分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# 文件存储配置
file:
  upload:
    # 文件上传根路径
    path: D:/uploads/library/
    # 访问域名
    domain: http://localhost:8080
    # 图书封面路径
    book-cover-path: images/books/
    # 导入导出路径
    import-export-path: files/import-export/
    # 模板文件路径
    template-path: templates/
    # 允许的图片格式
    allowed-image-formats: jpg,jpeg,png,gif
    # 最大文件大小（字节）
    max-file-size: 10485760

# 系统配置
system:
  # 默认密码
  default-password: 123456
  # 默认头像
  default-avatar: /images/default-avatar.png
  # 默认图书封面
  default-book-cover: /images/books/default-cover.jpg
  # 系统名称
  name: 图书管理系统
  # 系统版本
  version: 1.0.0

# 日志配置
logging:
  level:
    com.demo.mapper: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: logs/library-management.log
