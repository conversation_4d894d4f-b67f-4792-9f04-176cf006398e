<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
		PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="useColumnLabel" value="true" />
		<setting name="mapUnderscoreToCamelCase" value="true" />
		<setting name="jdbcTypeForNull" value="NULL"/>
	</settings>
	<plugins>
		<!-- com.github.pagehelper为PageHelper类所在包名 -->
		<plugin interceptor="com.github.pagehelper.PageInterceptor">
			<!-- 使用下面的方式配置参数，后面会有所有的参数介绍 -->
			<property name="supportMethodsArguments" value="true"/>
			<property name="params" value="pageNum=pageNumKey;pageSize=pageSizeKey;"/>
		</plugin>
	</plugins>

</configuration>