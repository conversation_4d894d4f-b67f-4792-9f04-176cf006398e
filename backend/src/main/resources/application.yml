server:
  port: ${register.node.port}
  servlet:
    context-path: /${spring.application.name}/

logging:
  config: classpath:logback-spring.xml

#指定swagger api访问方式        
springfox:
  documentation:
    swagger:
      v2:
        path: /swagger.json
info:
  status: ok
management:
  endpoints:
    web:
      base-path: /
      exposure:
        include: ["info", "refresh"]

mybatis:
  config-locations: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.demo.po
  configuration:
     jdbc-type-for-null: 'null'
     call-setters-on-nulls: true
     map-underscore-to-camel-case: true